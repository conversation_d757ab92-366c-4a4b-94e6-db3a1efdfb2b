# chess_player_gui.py
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import chess
import chess.engine
import chess.pgn
import torch
import numpy as np
from datetime import datetime
import threading
import time
from model import ChessNet
from utils import board_to_planes_fast, MOVE_TO_IDX, IDX_TO_MOVE

class ChessPlayerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("♟️ Chess AI Player vs Stockfish")
        self.root.geometry("800x600")
        
        # Game state
        self.board = chess.Board()
        self.game = chess.pgn.Game()
        self.node = self.game
        self.engine = None
        self.model = None
        self.device = torch.device("cpu")
        self.game_active = False
        
        self.setup_ui()
    
    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for controls
        control_frame = tk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Right panel for board display
        board_frame = tk.Frame(main_frame)
        board_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # === CONTROL PANEL ===
        row = 0
        
        # Load Stockfish
        tk.Button(control_frame, text="🐟 Load Stockfish.exe", command=self.load_stockfish, bg="lightblue").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        self.stockfish_label = tk.Label(control_frame, text="❌ Not loaded", fg="red")
        self.stockfish_label.grid(row=row+1, column=0, columnspan=2, sticky='w')
        
        row += 2
        
        # Load AI Model
        tk.Button(control_frame, text="🤖 Load AI Model (.pt)", command=self.load_model, bg="lightgreen").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        self.model_label = tk.Label(control_frame, text="❌ Not loaded", fg="red")
        self.model_label.grid(row=row+1, column=0, columnspan=2, sticky='w')
        
        row += 2
        
        # FEN Input
        tk.Label(control_frame, text="📋 FEN Position:").grid(row=row, column=0, columnspan=2, sticky='w')
        self.fen_entry = tk.Entry(control_frame, width=40)
        self.fen_entry.grid(row=row+1, column=0, columnspan=2, pady=2, sticky='ew')
        tk.Button(control_frame, text="Load FEN", command=self.load_fen).grid(row=row+2, column=0, pady=2, sticky='ew')
        tk.Button(control_frame, text="Reset Board", command=self.reset_board).grid(row=row+2, column=1, pady=2, sticky='ew')
        
        row += 3
        
        # Game controls
        tk.Label(control_frame, text="🎮 Game Controls:").grid(row=row, column=0, columnspan=2, sticky='w')
        
        # Player selection
        player_frame = tk.Frame(control_frame)
        player_frame.grid(row=row+1, column=0, columnspan=2, sticky='ew', pady=2)
        
        tk.Label(player_frame, text="You play as:").pack(side=tk.LEFT)
        self.player_color = tk.StringVar(value="white")
        tk.Radiobutton(player_frame, text="White", variable=self.player_color, value="white").pack(side=tk.LEFT)
        tk.Radiobutton(player_frame, text="Black", variable=self.player_color, value="black").pack(side=tk.LEFT)
        
        # Opponent selection
        opponent_frame = tk.Frame(control_frame)
        opponent_frame.grid(row=row+2, column=0, columnspan=2, sticky='ew', pady=2)
        
        tk.Label(opponent_frame, text="Opponent:").pack(side=tk.LEFT)
        self.opponent = tk.StringVar(value="stockfish")
        tk.Radiobutton(opponent_frame, text="Stockfish", variable=self.opponent, value="stockfish").pack(side=tk.LEFT)
        tk.Radiobutton(opponent_frame, text="AI Model", variable=self.opponent, value="ai").pack(side=tk.LEFT)
        
        row += 3
        
        # Start/Stop game
        tk.Button(control_frame, text="▶️ Start Game", command=self.start_game, bg="lightgreen").grid(row=row, column=0, pady=5, sticky='ew')
        tk.Button(control_frame, text="⏹️ Stop Game", command=self.stop_game, bg="lightcoral").grid(row=row, column=1, pady=5, sticky='ew')
        
        row += 1
        
        # Move input
        tk.Label(control_frame, text="Your move (e.g. e2e4):").grid(row=row, column=0, columnspan=2, sticky='w')
        self.move_entry = tk.Entry(control_frame, width=20)
        self.move_entry.grid(row=row+1, column=0, pady=2, sticky='ew')
        self.move_entry.bind('<Return>', self.make_move)
        tk.Button(control_frame, text="Move", command=self.make_move).grid(row=row+1, column=1, pady=2, sticky='ew')
        
        row += 2
        
        # AI Prediction
        tk.Button(control_frame, text="🔮 Get AI Prediction", command=self.get_ai_prediction, bg="lightyellow").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        self.prediction_label = tk.Label(control_frame, text="", fg="blue", wraplength=200)
        self.prediction_label.grid(row=row+1, column=0, columnspan=2, sticky='w')
        
        row += 2
        
        # Save game
        tk.Button(control_frame, text="💾 Save Game as PGN", command=self.save_pgn, bg="lightgray").grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')
        
        # Configure column weights
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        
        # === BOARD DISPLAY ===
        self.board_text = tk.Text(board_frame, font=("Courier", 12), width=50, height=25)
        self.board_text.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_label = tk.Label(self.root, text="Ready to play chess!", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Initial board display
        self.update_board_display()
    
    def load_stockfish(self):
        path = filedialog.askopenfilename(
            title="Select Stockfish executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if path:
            try:
                if self.engine:
                    self.engine.quit()
                self.engine = chess.engine.SimpleEngine.popen_uci(path)
                self.stockfish_label.config(text="✅ Stockfish loaded", fg="green")
                self.status_label.config(text=f"Stockfish loaded from: {path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load Stockfish:\n{str(e)}")
    
    def load_model(self):
        path = filedialog.askopenfilename(
            title="Select AI model checkpoint",
            filetypes=[("PyTorch files", "*.pt"), ("All files", "*.*")]
        )
        if path:
            try:
                self.model = ChessNet(num_moves=len(MOVE_TO_IDX))
                checkpoint = torch.load(path, map_location=self.device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.model.eval()
                self.model_label.config(text="✅ AI Model loaded", fg="green")
                self.status_label.config(text=f"AI model loaded from: {path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load AI model:\n{str(e)}")
    
    def load_fen(self):
        fen = self.fen_entry.get().strip()
        if fen:
            try:
                self.board = chess.Board(fen)
                self.update_board_display()
                self.status_label.config(text=f"Loaded FEN: {fen}")
            except Exception as e:
                messagebox.showerror("Error", f"Invalid FEN:\n{str(e)}")
    
    def reset_board(self):
        self.board = chess.Board()
        self.game = chess.pgn.Game()
        self.node = self.game
        self.update_board_display()
        self.status_label.config(text="Board reset to starting position")
    
    def update_board_display(self):
        board_str = str(self.board)
        self.board_text.delete(1.0, tk.END)
        self.board_text.insert(1.0, board_str)
        self.board_text.insert(tk.END, f"\n\nFEN: {self.board.fen()}")
        self.board_text.insert(tk.END, f"\nTurn: {'White' if self.board.turn else 'Black'}")
        
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn else "White"
            self.board_text.insert(tk.END, f"\n\n🏆 CHECKMATE! {winner} wins!")
        elif self.board.is_stalemate():
            self.board_text.insert(tk.END, f"\n\n🤝 STALEMATE! Draw!")
        elif self.board.is_check():
            self.board_text.insert(tk.END, f"\n\n⚠️ CHECK!")
    
    def start_game(self):
        if not self.engine and self.opponent.get() == "stockfish":
            messagebox.showerror("Error", "Please load Stockfish first!")
            return
        if not self.model and self.opponent.get() == "ai":
            messagebox.showerror("Error", "Please load AI model first!")
            return
        
        self.game_active = True
        self.status_label.config(text="Game started! Make your move.")
        
        # If player is black, make opponent move first
        if self.player_color.get() == "black":
            threading.Thread(target=self.make_opponent_move, daemon=True).start()
    
    def stop_game(self):
        self.game_active = False
        self.status_label.config(text="Game stopped.")
    
    def make_move(self, event=None):
        if not self.game_active:
            return
        
        move_str = self.move_entry.get().strip()
        if not move_str:
            return
        
        try:
            move = chess.Move.from_uci(move_str)
            if move in self.board.legal_moves:
                self.board.push(move)
                self.node = self.node.add_variation(move)
                self.move_entry.delete(0, tk.END)
                self.update_board_display()
                
                if self.board.is_game_over():
                    self.game_active = False
                    self.status_label.config(text="Game over!")
                else:
                    # Make opponent move
                    threading.Thread(target=self.make_opponent_move, daemon=True).start()
            else:
                messagebox.showerror("Error", "Illegal move!")
        except Exception as e:
            messagebox.showerror("Error", f"Invalid move format:\n{str(e)}")
    
    def make_opponent_move(self):
        if not self.game_active or self.board.is_game_over():
            return
        
        try:
            if self.opponent.get() == "stockfish" and self.engine:
                result = self.engine.play(self.board, chess.engine.Limit(time=1.0))
                move = result.move
            elif self.opponent.get() == "ai" and self.model:
                move = self.get_ai_move()
            else:
                return
            
            if move:
                self.board.push(move)
                self.node = self.node.add_variation(move)
                self.root.after(0, self.update_board_display)
                self.root.after(0, lambda: self.status_label.config(text=f"Opponent played: {move}"))
                
                if self.board.is_game_over():
                    self.game_active = False
                    self.root.after(0, lambda: self.status_label.config(text="Game over!"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Opponent move failed:\n{str(e)}"))
    
    def get_ai_move(self):
        if not self.model:
            return None
        
        try:
            # Convert board to input tensor
            planes = board_to_planes_fast(self.board)
            x = torch.tensor(planes).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                policy_logits, _ = self.model(x)
                
            # Apply legal move mask
            legal_moves = list(self.board.legal_moves)
            if not legal_moves:
                return None
            
            # Get probabilities for legal moves
            move_probs = []
            for move in legal_moves:
                uci = move.uci()
                if uci in MOVE_TO_IDX:
                    idx = MOVE_TO_IDX[uci]
                    prob = torch.softmax(policy_logits, dim=1)[0, idx].item()
                    move_probs.append((move, prob))
            
            if move_probs:
                # Choose move with highest probability
                best_move = max(move_probs, key=lambda x: x[1])[0]
                return best_move
            else:
                # Fallback to random legal move
                return np.random.choice(legal_moves)
        except Exception as e:
            print(f"AI move error: {e}")
            return np.random.choice(list(self.board.legal_moves)) if self.board.legal_moves else None
    
    def get_ai_prediction(self):
        if not self.model:
            messagebox.showerror("Error", "Please load AI model first!")
            return
        
        try:
            move = self.get_ai_move()
            if move:
                self.prediction_label.config(text=f"AI suggests: {move}")
            else:
                self.prediction_label.config(text="AI couldn't find a move")
        except Exception as e:
            messagebox.showerror("Error", f"Prediction failed:\n{str(e)}")
    
    def save_pgn(self):
        if not self.game.mainline_moves():
            messagebox.showwarning("Warning", "No moves to save!")
            return
        
        path = filedialog.asksaveasfilename(
            title="Save game as PGN",
            defaultextension=".pgn",
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        
        if path:
            try:
                # Set game headers
                self.game.headers["Event"] = "AI vs Human"
                self.game.headers["Date"] = datetime.now().strftime("%Y.%m.%d")
                self.game.headers["White"] = "Human" if self.player_color.get() == "white" else "AI"
                self.game.headers["Black"] = "AI" if self.player_color.get() == "white" else "Human"
                
                with open(path, "w") as f:
                    print(self.game, file=f)
                
                messagebox.showinfo("Success", f"Game saved to: {path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save game:\n{str(e)}")
    
    def __del__(self):
        if self.engine:
            self.engine.quit()

if __name__ == "__main__":
    root = tk.Tk()
    app = ChessPlayerGUI(root)
    root.mainloop()

# dataloader.py
import chess.pgn
import numpy as np
import torch
from tqdm import tqdm
from torch.utils.data import Dataset
from utils import board_to_planes_fast, get_move_legality_mask, MOVE_TO_IDX

class ChessDataset(Dataset):
    def __init__(self, pgn_path, max_games=None, augment=True):
        self.positions = []
        self.moves = []
        self.results = []
        self.masks = []  # legality masks
        self.augment = augment
        self.load_pgn(pgn_path, max_games)

    def mirror_board(self, board):
        """Return a flipped version of the board for augmentation."""
        flipped = chess.Board.empty()
        for sq in chess.SQUARES:
            piece = board.piece_at(sq)
            if piece:
                new_sq = chess.square(7 - (sq % 8), 7 - (sq // 8))
                flipped.set_piece_at(new_sq, piece)
        flipped.turn = not board.turn
        flipped.castling_rights = 0
        if board.has_kingside_castling_rights(chess.WHITE):
            flipped.set_castling_fen('k')
        if board.has_queenside_castling_rights(chess.WHITE):
            flipped.set_castling_fen('q')
        if board.has_kingside_castling_rights(chess.BLACK):
            flipped.set_castling_fen('K')
        if board.has_queenside_castling_rights(chess.BLACK):
            flipped.set_castling_fen('Q')
        return flipped

    def load_pgn(self, filepath, max_games):
        with open(filepath) as f:
            game_count = 0
            with tqdm(desc="Loading PGN") as pbar:
                while True:
                    try:
                        game = chess.pgn.read_game(f)
                        if game is None:
                            break
                        if max_games and game_count >= max_games:
                            break

                        board = game.board()
                        result_str = game.headers.get("Result", "½-½")
                        result = {'1-0': 1.0, '0-1': 0.0, '1/2-1/2': 0.5}.get(result_str, 0.5)

                        for move in game.mainline_moves():
                            if len(board.move_stack) < 8:
                                board.push(move)
                                continue

                            uci = move.uci()
                            if uci not in MOVE_TO_IDX:
                                board.push(move)
                                continue

                            # Augment: random flip
                            flips = [False]
                            if self.augment and np.random.rand() < 0.5:
                                flips.append(True)

                            for flip in flips:
                                aug_board = board.copy() if not flip else self.mirror_board(board)
                                plane = board_to_planes_fast(aug_board, flip=flip)
                                move_idx = get_move_index(uci, flip)
                                if move_idx is None:
                                    continue

                                mask = get_move_legality_mask(aug_board)

                                self.positions.append(plane)
                                self.moves.append(move_idx)
                                self.results.append(result)
                                self.masks.append(mask)

                            board.push(move)

                        game_count += 1
                        pbar.update(1)
                    except Exception as e:
                        print(f"Error processing game {game_count}: {e}")
                        continue

        print(f"Loaded {len(self.positions)} positions from {game_count} games")

    def __len__(self):
        return len(self.positions)

    def __getitem__(self, idx):
        return (
            self.positions[idx],
            self.moves[idx],
            self.results[idx],
            self.masks[idx]
        )

def get_move_index(uci, flipped):
    """Get move index, adjusting for flipped board if needed."""
    from utils import MOVE_TO_IDX  # Import locally to ensure availability
    if flipped:
        try:
            from_sq = uci[0:2]
            to_sq = uci[2:4]
            prom = uci[4:] if len(uci) > 4 else ""
            fr_file, fr_rank = from_sq[0], int(from_sq[1])
            to_file, to_rank = to_sq[0], int(to_sq[1])
            new_from = f"{7 - ord(fr_file) + ord('a')} {8 - fr_rank}"
            new_to = f"{7 - ord(to_file) + ord('a')} {8 - to_rank}"
            new_uci = f"{new_from}{new_to}{prom}".replace(" ", "")
            return MOVE_TO_IDX.get(new_uci)
        except:
            return None
    return MOVE_TO_IDX.get(uci)

def collate_fn(batch):
    states, moves, results, masks = zip(*batch)
    return (
        torch.tensor(np.array(states)),
        torch.tensor(np.array(moves)),
        torch.tensor(np.array(results), dtype=torch.float32),
        torch.tensor(np.array(masks))
    )
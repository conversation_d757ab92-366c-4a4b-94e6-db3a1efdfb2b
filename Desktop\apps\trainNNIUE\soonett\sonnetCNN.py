import os
import time
import threading
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import chess.pgn
import numpy as np
from tkinter import *
from tkinter import filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from tqdm import tqdm

# ------------------ CNN MODEL ------------------ #
class ChessCNN(nn.Module):
    def __init__(self):
        super(ChessCNN, self).__init__()
        self.conv1 = nn.Conv2d(12, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.fc1 = nn.Linear(64 * 8 * 8, 512)
        self.fc2 = nn.Linear(512, 4672)  # 4672 = 64*73 (legal move indices)

    def forward(self, x):
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = x.view(x.size(0), -1)
        x = torch.relu(self.fc1(x))
        return self.fc2(x)

# ------------------ PGN DATASET ------------------ #
class ChessDataset(Dataset):
    def __init__(self, folder):
        self.data = []
        for filename in os.listdir(folder):
            if filename.endswith(".pgn"):
                path = os.path.join(folder, filename)
                with open(path) as f:
                    while True:
                        game = chess.pgn.read_game(f)
                        if game is None:
                            break
                        board = game.board()
                        for move in game.mainline_moves():
                            x = self.board_to_tensor(board)
                            y = self.move_to_index(move)
                            self.data.append((x, y))
                            board.push(move)

    def board_to_tensor(self, board):
        tensor = np.zeros((12, 8, 8), dtype=np.float32)
        piece_map = board.piece_map()
        for square, piece in piece_map.items():
            idx = (piece.piece_type - 1) + (0 if piece.color == chess.WHITE else 6)
            tensor[idx, square // 8, square % 8] = 1
        return tensor

    def move_to_index(self, move):
        return move.from_square * 73 + move.to_square  # crude encoding

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        x, y = self.data[idx]
        return torch.tensor(x), torch.tensor(y)

# ------------------ GUI APP ------------------ #
class TrainerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("ChessNet Trainer")
        self.train_path = StringVar()
        self.val_path = StringVar()

        # Parameters
        self.epochs = IntVar(value=200)
        self.batch_size = IntVar(value=256)
        self.lr = DoubleVar(value=0.001)
        self.weight_decay = DoubleVar(value=1e-6)
        self.val_interval = IntVar(value=10)
        self.ckpt_interval = IntVar(value=1)
        self.patience = IntVar(value=200)
        self.auto_save = BooleanVar()
        self.auto_load = BooleanVar()
        self.time_ckpt = BooleanVar()

        self.model = ChessCNN()
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr.get(), weight_decay=self.weight_decay.get())
        self.criterion = nn.CrossEntropyLoss()

        self.train_loader = None
        self.val_loader = None

        self.loss_history = []
        self.val_loss_history = []

        self.build_gui()

    def build_gui(self):
        # File selection
        top_frame = Frame(self.root)
        top_frame.pack()
        Label(top_frame, text="Training PGN Folder:").grid(row=0, column=0)
        Entry(top_frame, textvariable=self.train_path, width=50).grid(row=0, column=1)
        Button(top_frame, text="Browse", command=self.select_train_folder).grid(row=0, column=2)

        Label(top_frame, text="Validation PGN Folder:").grid(row=1, column=0)
        Entry(top_frame, textvariable=self.val_path, width=50).grid(row=1, column=1)
        Button(top_frame, text="Browse", command=self.select_val_folder).grid(row=1, column=2)

        # Controls
        control_frame = Frame(self.root)
        control_frame.pack()
        Button(control_frame, text="Start Training", command=self.start_training).grid(row=0, column=0)
        Button(control_frame, text="Stop Training", command=self.stop_training).grid(row=0, column=1)
        Button(control_frame, text="Run Validation", command=self.run_validation).grid(row=0, column=2)
        Button(control_frame, text="Load Checkpoint", command=self.load_checkpoint).grid(row=0, column=3)
        Button(control_frame, text="Save Checkpoint", command=self.save_checkpoint).grid(row=0, column=4)

        # Configuration
        config_frame = Frame(self.root)
        config_frame.pack()
        Label(config_frame, text="Epochs:").grid(row=0, column=0)
        Entry(config_frame, textvariable=self.epochs).grid(row=0, column=1)
        Label(config_frame, text="Batch Size:").grid(row=0, column=2)
        Entry(config_frame, textvariable=self.batch_size).grid(row=0, column=3)

        Label(config_frame, text="Learning Rate:").grid(row=1, column=0)
        Entry(config_frame, textvariable=self.lr).grid(row=1, column=1)
        Label(config_frame, text="Weight Decay:").grid(row=1, column=2)
        Entry(config_frame, textvariable=self.weight_decay).grid(row=1, column=3)

        Label(config_frame, text="Validation Interval:").grid(row=2, column=0)
        Entry(config_frame, textvariable=self.val_interval).grid(row=2, column=1)
        Label(config_frame, text="Checkpoint Interval:").grid(row=2, column=2)
        Entry(config_frame, textvariable=self.ckpt_interval).grid(row=2, column=3)

        Label(config_frame, text="Early Stopping Patience:").grid(row=3, column=0)
        Entry(config_frame, textvariable=self.patience).grid(row=3, column=1)

        Checkbutton(config_frame, text="Auto-save Best Model", variable=self.auto_save).grid(row=4, column=0)
        Checkbutton(config_frame, text="Auto-load Latest Checkpoint on Start", variable=self.auto_load).grid(row=4, column=1)
        Checkbutton(config_frame, text="Enable Time-Based Checkpoints", variable=self.time_ckpt).grid(row=4, column=2)

        Button(config_frame, text="Apply Configuration", command=self.apply_config).grid(row=5, column=0)

        # Progress & plot
        self.progress_label = Label(self.root, text="Training Progress")
        self.progress_label.pack()

        self.figure = Figure(figsize=(6, 3))
        self.ax = self.figure.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.figure, master=self.root)
        self.canvas.get_tk_widget().pack()

    def select_train_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.train_path.set(folder)

    def select_val_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.val_path.set(folder)

    def apply_config(self):
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr.get(), weight_decay=self.weight_decay.get())

    def start_training(self):
        if not self.train_loader:
            train_data = ChessDataset(self.train_path.get())
            self.train_loader = DataLoader(train_data, batch_size=self.batch_size.get(), shuffle=True)
        if not self.val_loader:
            val_data = ChessDataset(self.val_path.get())
            self.val_loader = DataLoader(val_data, batch_size=self.batch_size.get(), shuffle=False)

        threading.Thread(target=self.train_loop).start()

    def stop_training(self):
        self.training = False

    def train_loop(self):
        self.training = True
        best_loss = float('inf')
        last_val_epoch = 0
        last_ckpt_time = time.time()

        for epoch in range(self.epochs.get()):
            if not self.training:
                break
            self.model.train()
            total_loss = 0
            for X, y in tqdm(self.train_loader, desc=f"Epoch {epoch+1}/{self.epochs.get()} [Training]"):
                pred = self.model(X)
                loss = self.criterion(pred, y)
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()
                total_loss += loss.item()

            avg_loss = total_loss / len(self.train_loader)
            self.loss_history.append(avg_loss)

            if (epoch + 1) % self.val_interval.get() == 0:
                val_loss = self.evaluate()
                self.val_loss_history.append(val_loss)

            if (epoch + 1) % self.ckpt_interval.get() == 0 or (self.time_ckpt.get() and time.time() - last_ckpt_time > 60):
                self.save_checkpoint()
                last_ckpt_time = time.time()

            self.progress_label.config(text=f"Epoch {epoch+1}/{self.epochs.get()}, Train Loss: {avg_loss:.4f}")
            self.update_plot()

    def evaluate(self):
        self.model.eval()
        total_loss = 0
        with torch.no_grad():
            for X, y in tqdm(self.val_loader, desc="Validation"):
                pred = self.model(X)
                loss = self.criterion(pred, y)
                total_loss += loss.item()
        return total_loss / len(self.val_loader)

    def update_plot(self):
        self.ax.clear()
        self.ax.plot(self.loss_history, label="Train Loss")
        self.ax.plot(self.val_loss_history, label="Validation Loss")
        self.ax.legend()
        self.ax.set_title("Training and Validation Loss")
        self.ax.set_ylabel("Loss")
        self.canvas.draw()

    def save_checkpoint(self):
        torch.save(self.model.state_dict(), "chessnet_ckpt.pt")

    def load_checkpoint(self):
        if os.path.exists("chessnet_ckpt.pt"):
            self.model.load_state_dict(torch.load("chessnet_ckpt.pt"))
            messagebox.showinfo("Checkpoint", "Checkpoint loaded successfully.")

    def run_validation(self):
        if self.val_loader:
            val_loss = self.evaluate()
            self.progress_label.config(text=f"Validation Loss: {val_loss:.4f}")
            self.val_loss_history.append(val_loss)
            self.update_plot()

# ------------------ MAIN ------------------ #
if __name__ == '__main__':
    root = Tk()
    app = TrainerApp(root)
    root.mainloop()

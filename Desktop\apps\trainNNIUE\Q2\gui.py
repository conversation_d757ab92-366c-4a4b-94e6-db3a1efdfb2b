# gui.py
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import threading
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import os
from train import train_model
from dataloader import ChessDataset, collate_fn

class ChessTrainerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("♟️ Chess AI Trainer")
        self.train_loader = None
        self.val_loader = None
        self.resume_path = None
        self.setup_ui()

    def setup_ui(self):
        row = 0
        tk.Button(self.root, text="📂 Load Training Data", command=self.load_train).grid(row=row, column=0, pady=5, padx=5)
        self.train_label = tk.Label(self.root, text="❌ Not loaded")
        self.train_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Button(self.root, text="📂 Load Validation Data", command=self.load_val).grid(row=row, column=0, pady=5, padx=5)
        self.val_label = tk.Label(self.root, text="❌ Not loaded")
        self.val_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Button(self.root, text="🔁 Resume from Checkpoint", command=self.load_checkpoint).grid(row=row, column=0, pady=5, padx=5)
        self.ckpt_label = tk.Label(self.root, text="❌ None")
        self.ckpt_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="_epochs:").grid(row=row, column=0, sticky='e')
        self.epochs_entry = tk.Entry(self.root, width=10)
        self.epochs_entry.insert(0, "10")
        self.epochs_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        self.start_btn = tk.Button(self.root, text="▶️ Start Training", command=self.start_training, bg="lightgreen")
        self.start_btn.grid(row=row, column=0, columnspan=2, pady=10)

        # Plot
        row += 1
        self.figure, self.ax = plt.subplots(figsize=(6, 4))
        self.canvas = FigureCanvasTkAgg(self.figure, self.root)
        self.canvas.get_tk_widget().grid(row=row, column=0, columnspan=2, pady=10)

    def load_train(self):
        path = filedialog.askopenfilename(title="Select Training PGN", filetypes=[("PGN Files", "*.pgn")])
        if path:
            self.train_dataset = ChessDataset(path, max_games=500, augment=True)
            self.train_loader = DataLoader(self.train_dataset, batch_size=64, shuffle=True, num_workers=0, collate_fn=collate_fn)
            self.train_label.config(text="✅ " + os.path.basename(path)[:30])

    def load_val(self):
        path = filedialog.askopenfilename(title="Select Validation PGN", filetypes=[("PGN Files", "*.pgn")])
        if path:
            self.val_dataset = ChessDataset(path, max_games=100, augment=False)
            self.val_loader = DataLoader(self.val_dataset, batch_size=64, shuffle=False, num_workers=0, collate_fn=collate_fn)
            self.val_label.config(text="✅ " + os.path.basename(path)[:30])

    def load_checkpoint(self):
        path = filedialog.askopenfilename(title="Select Checkpoint", filetypes=[("PT Files", "*.pt")])
        if path:
            self.resume_path = path
            self.ckpt_label.config(text="✅ " + os.path.basename(path))

    def start_training(self):
        if not self.train_loader or not self.val_loader:
            messagebox.showerror("Error", "Please load both training and validation data!")
            return

        epochs = self.epochs_entry.get()
        if not epochs.isdigit():
            messagebox.showerror("Error", "Enter valid number of epochs!")
            return

        self.epochs = int(epochs)
        threading.Thread(target=self.run_training, daemon=True).start()

    def run_training(self):
        try:
            train_model(self.train_loader, self.val_loader, resume_path=self.resume_path, num_epochs=self.epochs)
            messagebox.showinfo("Success", "🎉 Training completed!")
        except Exception as e:
            messagebox.showerror("Error", f"❌ Training failed:\n{str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = ChessTrainerGUI(root)
    root.mainloop()
# gui.py
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import threading
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import os
from train import train_model
from dataloader import ChessDataset, collate_fn
from torch.utils.data import DataLoader

class ChessTrainerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("♟️ Chess AI Trainer")
        self.train_loader = None
        self.val_loader = None
        self.resume_path = None
        self.setup_ui()

    def setup_ui(self):
        row = 0
        tk.Button(self.root, text="📂 Load Training Data", command=self.load_train).grid(row=row, column=0, pady=5, padx=5)
        self.train_label = tk.Label(self.root, text="❌ Not loaded")
        self.train_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Button(self.root, text="📂 Load Validation Data", command=self.load_val).grid(row=row, column=0, pady=5, padx=5)
        self.val_label = tk.Label(self.root, text="❌ Not loaded")
        self.val_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Button(self.root, text="🔁 Resume from Checkpoint", command=self.load_checkpoint).grid(row=row, column=0, pady=5, padx=5)
        self.ckpt_label = tk.Label(self.root, text="❌ None")
        self.ckpt_label.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="🎯 Max Train Games:").grid(row=row, column=0, sticky='e')
        self.max_train_games_entry = tk.Entry(self.root, width=10)
        self.max_train_games_entry.insert(0, "ALL")
        self.max_train_games_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="🎯 Max Val Games:").grid(row=row, column=0, sticky='e')
        self.max_val_games_entry = tk.Entry(self.root, width=10)
        self.max_val_games_entry.insert(0, "ALL")
        self.max_val_games_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        tk.Label(self.root, text="📊 Epochs:").grid(row=row, column=0, sticky='e')
        self.epochs_entry = tk.Entry(self.root, width=10)
        self.epochs_entry.insert(0, "10")
        self.epochs_entry.grid(row=row, column=1, sticky='w', padx=5)

        row += 1
        self.start_btn = tk.Button(self.root, text="▶️ Start Training", command=self.start_training, bg="lightgreen")
        self.start_btn.grid(row=row, column=0, columnspan=2, pady=10)

        # Plot
        row += 1
        self.figure, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(12, 4))
        self.canvas = FigureCanvasTkAgg(self.figure, self.root)
        self.canvas.get_tk_widget().grid(row=row, column=0, columnspan=2, pady=10)

        # Initialize plot data
        self.train_losses = {'policy': [], 'value': [], 'acc': []}
        self.val_losses = {'policy': [], 'value': [], 'acc': []}
        self.epochs_data = []

        self.setup_plots()

    def setup_plots(self):
        """Initialize the loss plots"""
        # Loss plot
        self.ax1.set_title('Training & Validation Loss')
        self.ax1.set_xlabel('Epoch')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.legend()

        # Accuracy plot
        self.ax2.set_title('Training & Validation Accuracy')
        self.ax2.set_xlabel('Epoch')
        self.ax2.set_ylabel('Accuracy (%)')
        self.ax2.grid(True, alpha=0.3)
        self.ax2.legend()

        plt.tight_layout()
        self.canvas.draw()

    def update_plots(self, epoch, train_policy_loss, train_value_loss, train_acc,
                     val_policy_loss, val_value_loss, val_acc):
        """Update the plots with new data"""
        # Store data
        self.epochs_data.append(epoch)
        self.train_losses['policy'].append(train_policy_loss)
        self.train_losses['value'].append(train_value_loss)
        self.train_losses['acc'].append(train_acc)
        self.val_losses['policy'].append(val_policy_loss)
        self.val_losses['value'].append(val_value_loss)
        self.val_losses['acc'].append(val_acc)

        # Clear and redraw plots
        self.ax1.clear()
        self.ax2.clear()

        # Loss plot
        self.ax1.plot(self.epochs_data, self.train_losses['policy'], 'b-', label='Train Policy Loss', linewidth=2)
        self.ax1.plot(self.epochs_data, self.train_losses['value'], 'b--', label='Train Value Loss', linewidth=2)
        self.ax1.plot(self.epochs_data, self.val_losses['policy'], 'r-', label='Val Policy Loss', linewidth=2)
        self.ax1.plot(self.epochs_data, self.val_losses['value'], 'r--', label='Val Value Loss', linewidth=2)
        self.ax1.set_title('Training & Validation Loss')
        self.ax1.set_xlabel('Epoch')
        self.ax1.set_ylabel('Loss')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.legend()

        # Accuracy plot
        self.ax2.plot(self.epochs_data, self.train_losses['acc'], 'b-', label='Train Accuracy', linewidth=2)
        self.ax2.plot(self.epochs_data, self.val_losses['acc'], 'r-', label='Val Accuracy', linewidth=2)
        self.ax2.set_title('Training & Validation Accuracy')
        self.ax2.set_xlabel('Epoch')
        self.ax2.set_ylabel('Accuracy (%)')
        self.ax2.grid(True, alpha=0.3)
        self.ax2.legend()

        plt.tight_layout()
        self.canvas.draw()

    def load_train(self):
        path = filedialog.askopenfilename(title="Select Training PGN", filetypes=[("PGN Files", "*.pgn")])
        if path:
            max_games_str = self.max_train_games_entry.get().strip().upper()
            max_games = None if max_games_str == "ALL" else int(max_games_str) if max_games_str.isdigit() else 500

            self.train_dataset = ChessDataset(path, max_games=max_games, augment=True)
            self.train_loader = DataLoader(self.train_dataset, batch_size=64, shuffle=True, num_workers=0, collate_fn=collate_fn)
            games_text = "ALL" if max_games is None else str(max_games)
            self.train_label.config(text=f"✅ {os.path.basename(path)[:20]} ({games_text} games)")

    def load_val(self):
        path = filedialog.askopenfilename(title="Select Validation PGN", filetypes=[("PGN Files", "*.pgn")])
        if path:
            max_games_str = self.max_val_games_entry.get().strip().upper()
            max_games = None if max_games_str == "ALL" else int(max_games_str) if max_games_str.isdigit() else 100

            self.val_dataset = ChessDataset(path, max_games=max_games, augment=False)
            self.val_loader = DataLoader(self.val_dataset, batch_size=64, shuffle=False, num_workers=0, collate_fn=collate_fn)
            games_text = "ALL" if max_games is None else str(max_games)
            self.val_label.config(text=f"✅ {os.path.basename(path)[:20]} ({games_text} games)")

    def load_checkpoint(self):
        path = filedialog.askopenfilename(title="Select Checkpoint", filetypes=[("PT Files", "*.pt")])
        if path:
            self.resume_path = path
            self.ckpt_label.config(text="✅ " + os.path.basename(path))

    def start_training(self):
        if not self.train_loader or not self.val_loader:
            messagebox.showerror("Error", "Please load both training and validation data!")
            return

        epochs = self.epochs_entry.get()
        if not epochs.isdigit():
            messagebox.showerror("Error", "Enter valid number of epochs!")
            return

        self.epochs = int(epochs)
        # Clear previous plot data
        self.train_losses = {'policy': [], 'value': [], 'acc': []}
        self.val_losses = {'policy': [], 'value': [], 'acc': []}
        self.epochs_data = []
        self.setup_plots()

        threading.Thread(target=self.run_training, daemon=True).start()

    def run_training(self):
        try:
            self.train_model_with_gui(self.train_loader, self.val_loader,
                                    resume_path=self.resume_path, num_epochs=self.epochs)
            self.root.after(0, lambda: messagebox.showinfo("Success", "🎉 Training completed!"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"❌ Training failed:\n{str(e)}"))

    def train_model_with_gui(self, train_loader, val_loader, resume_path=None, num_epochs=10, save_dir="checkpoints"):
        """Custom training function with GUI callbacks for real-time plotting"""
        import torch
        import torch.nn.functional as F
        from model import ChessNet
        from utils import MOVE_TO_IDX
        import time
        import os

        device = torch.device("cpu")
        model = ChessNet(num_moves=len(MOVE_TO_IDX))
        optimizer = torch.optim.AdamW(model.parameters(), lr=3e-4, weight_decay=1e-4)

        start_epoch = 0
        if resume_path:
            print(f"🔁 Loading checkpoint: {resume_path}")
            checkpoint = torch.load(resume_path, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            start_epoch = checkpoint['epoch'] + 1

        os.makedirs(save_dir, exist_ok=True)
        last_save_time = time.time()
        epoch = start_epoch

        for epoch in range(start_epoch, num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            t0 = time.time()

            # Training epoch
            model.train()
            total_policy_loss = 0.0
            total_value_loss = 0.0
            correct = 0
            total = 0

            for x, y_policy, y_value, legality_mask in train_loader:
                x = x.to(device)
                y_policy = y_policy.to(device)
                y_value = y_value.to(device)
                legality_mask = legality_mask.to(device)

                optimizer.zero_grad()
                policy_logits, value_pred = model(x)

                # Apply legality mask
                policy_logits = policy_logits + legality_mask

                policy_loss = F.cross_entropy(policy_logits, y_policy)
                value_loss = F.mse_loss(value_pred.squeeze(), y_value)
                loss = policy_loss + value_loss

                loss.backward()
                optimizer.step()

                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                pred = policy_logits.argmax(dim=1)
                correct += pred.eq(y_policy).sum().item()
                total += y_policy.size(0)

            train_policy_loss = total_policy_loss / len(train_loader)
            train_value_loss = total_value_loss / len(train_loader)
            train_acc = 100. * correct / total

            # Validation epoch
            model.eval()
            val_policy_loss = 0.0
            val_value_loss = 0.0
            correct = 0
            total = 0

            with torch.no_grad():
                for x, y_policy, y_value, legality_mask in val_loader:
                    x = x.to(device)
                    y_policy = y_policy.to(device)
                    y_value = y_value.to(device)
                    legality_mask = legality_mask.to(device)

                    policy_logits, value_pred = model(x)
                    policy_logits = policy_logits + legality_mask

                    policy_loss = F.cross_entropy(policy_logits, y_policy)
                    value_loss = F.mse_loss(value_pred.squeeze(), y_value)

                    val_policy_loss += policy_loss.item()
                    val_value_loss += value_loss.item()
                    pred = policy_logits.argmax(dim=1)
                    correct += pred.eq(y_policy).sum().item()
                    total += y_policy.size(0)

            val_policy_loss /= len(val_loader)
            val_value_loss /= len(val_loader)
            val_acc = 100. * correct / total
            t1 = time.time()

            print(f"Train: P={train_policy_loss:.4f}, V={train_value_loss:.4f}, Acc={train_acc:.2f}% | "
                  f"Val: P={val_policy_loss:.4f}, V={val_value_loss:.4f}, Acc={val_acc:.2f}% | Time: {t1-t0:.1f}s")

            # Update GUI plots in main thread
            self.root.after(0, lambda e=epoch+1, tpl=train_policy_loss, tvl=train_value_loss, ta=train_acc,
                           vpl=val_policy_loss, vvl=val_value_loss, va=val_acc:
                           self.update_plots(e, tpl, tvl, ta, vpl, vvl, va))

            # Save checkpoints
            if (epoch + 1) % 2 == 0:
                checkpoint_path = f"{save_dir}/checkpoint_epoch_{epoch+1}.pt"
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                }, checkpoint_path)
                print(f"✅ Checkpoint saved: {checkpoint_path}")

        # Final save
        final_path = f"{save_dir}/final.pt"
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
        }, final_path)
        print(f"✅ Final checkpoint saved: {final_path}")

        return model

if __name__ == "__main__":
    root = tk.Tk()
    app = ChessTrainerGUI(root)
    root.mainloop()
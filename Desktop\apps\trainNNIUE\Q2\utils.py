# utils.py
import chess
import numpy as np

def generate_move_list():
    """Generate all possible legal move UCI strings in chess (including underpromotions)."""
    board = chess.Board()
    moves = set()

    # All normal moves
    for move in board.legal_moves:
        moves.add(move.uci())

    # Add underpromotions
    for file in 'abcdefgh':
        # White promotion (7 -> 8)
        for promo in ['n', 'b', 'r']:
            moves.add(f'{file}7{file}8={promo}')
        # Black promotion (2 -> 1)
        for promo in ['n', 'b', 'r']:
            moves.add(f'{file}2{file}1={promo}')

    return sorted(moves)

# Global move list
MOVE_LIST = generate_move_list()
MOVE_TO_IDX = {move: idx for idx, move in enumerate(MOVE_LIST)}
IDX_TO_MOVE = {idx: move for idx, move in enumerate(MOVE_LIST)}
NUM_MOVES = len(MOVE_LIST)

def board_to_planes_fast(board, flip=False):
    """
    Convert python-chess board to 14x8x8 input tensor using bitboards.
    Optionally flip the board (for data augmentation).
    """
    planes = np.zeros((14, 8, 8), dtype=np.float32)
    piece_types = [chess.PAWN, chess.KNIGHT, chess.BISHOP, chess.ROOK, chess.QUEEN, chess.KING]

    for color, offset in [(chess.WHITE, 0), (chess.BLACK, 6)]:
        for pt in piece_types:
            idx = piece_types.index(pt)
            bb = board.pieces(pt, color)
            if flip:
                bb = chess.flip_vertical(chess.flip_horizontal(bb))
            for sq in bb:
                r, f = 7 - (sq // 8), sq % 8
                if flip:
                    r, f = 7 - r, 7 - f
                planes[offset + idx, r, f] = 1.0

    # Castling rights (simplified encoding)
    if board.has_kingside_castling_rights(chess.WHITE):
        planes[12, 0, 7] = 1.0
    if board.has_queenside_castling_rights(chess.WHITE):
        planes[12, 0, 0] = 1.0
    if board.has_kingside_castling_rights(chess.BLACK):
        planes[12, 7, 7] = 1.0
    if board.has_queenside_castling_rights(chess.BLACK):
        planes[12, 7, 0] = 1.0

    # Side to move
    planes[13, :, :] = 1.0 if board.turn == chess.WHITE else 0.0

    return planes

def get_move_legality_mask(board):
    """Return a -inf mask for illegal moves, 0 for legal moves. Shape: (1880,)"""
    mask = np.full((NUM_MOVES,), float('-inf'), dtype=np.float32)
    for move in board.legal_moves:
        uci = move.uci()
        if uci in MOVE_TO_IDX:
            mask[MOVE_TO_IDX[uci]] = 0.0
    return mask